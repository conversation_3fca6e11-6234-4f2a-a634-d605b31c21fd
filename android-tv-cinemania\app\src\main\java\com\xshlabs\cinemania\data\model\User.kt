package com.xshlabs.cinemania.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Profile(
    val id: String,
    @SerialName("full_name") val fullName: String?,
    val username: String?,
    @SerialName("avatar_url") val avatarUrl: String?,
    @SerialName("created_at") val createdAt: String,
    @SerialName("updated_at") val updatedAt: String
)

@Serializable
data class WatchlistItem(
    val id: String,
    @SerialName("user_id") val userId: String,
    @SerialName("tmdb_id") val tmdbId: Int,
    @SerialName("media_type") val mediaType: String, // "movie" or "tv"
    val title: String?,
    @SerialName("poster_path") val posterPath: String?,
    @SerialName("backdrop_path") val backdropPath: String?,
    @SerialName("release_date") val releaseDate: String?,
    @SerialName("vote_average") val voteAverage: Double?,
    @SerialName("imdb_id") val imdbId: String?,
    @SerialName("created_at") val createdAt: String
)

@Serializable
data class WatchProgress(
    val id: String,
    @SerialName("user_id") val userId: String,
    @SerialName("tmdb_id") val tmdbId: Int,
    @SerialName("media_type") val mediaType: String, // "movie" or "tv"
    val title: String?,
    @SerialName("poster_path") val posterPath: String?,
    @SerialName("backdrop_path") val backdropPath: String?,
    @SerialName("release_date") val releaseDate: String?,
    @SerialName("vote_average") val voteAverage: Double?,
    @SerialName("imdb_id") val imdbId: String?,
    @SerialName("current_time") val currentTime: Long, // in seconds
    @SerialName("total_duration") val totalDuration: Long, // in seconds
    @SerialName("progress_percentage") val progressPercentage: Double,
    @SerialName("season_number") val seasonNumber: Int? = null,
    @SerialName("episode_number") val episodeNumber: Int? = null,
    @SerialName("is_completed") val isCompleted: Boolean = false,
    @SerialName("last_watched") val lastWatched: String,
    @SerialName("created_at") val createdAt: String,
    @SerialName("updated_at") val updatedAt: String
)

@Serializable
data class UserSettings(
    val id: String,
    @SerialName("user_id") val userId: String,
    @SerialName("preferred_source") val preferredSource: String = "vidsrc",
    @SerialName("ad_block_enabled") val adBlockEnabled: Boolean = true,
    @SerialName("autoplay_enabled") val autoplayEnabled: Boolean = true,
    @SerialName("subtitles_enabled") val subtitlesEnabled: Boolean = true,
    @SerialName("preferred_quality") val preferredQuality: String = "1080p",
    @SerialName("dark_mode") val darkMode: Boolean = true,
    @SerialName("created_at") val createdAt: String,
    @SerialName("updated_at") val updatedAt: String
)

// Local data classes for UI
data class ContinueWatchingItem(
    val tmdbId: Int,
    val mediaType: MediaType,
    val title: String,
    val posterPath: String?,
    val backdropPath: String?,
    val progressPercentage: Double,
    val lastWatched: String,
    val seasonNumber: Int? = null,
    val episodeNumber: Int? = null
)

data class UserProfile(
    val id: String,
    val email: String,
    val fullName: String?,
    val username: String?,
    val avatarUrl: String?
)
