<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Cinemania</string>
    
    <!-- Navigation -->
    <string name="nav_home">Home</string>
    <string name="nav_movies">Movies</string>
    <string name="nav_series">Series</string>
    <string name="nav_search">Search</string>
    <string name="nav_profile">Profile</string>
    <string name="nav_settings">Settings</string>
    
    <!-- Home Screen -->
    <string name="home_featured">Featured</string>
    <string name="home_trending_movies">Trending Movies</string>
    <string name="home_popular_movies">Popular Movies</string>
    <string name="home_trending_tv">Trending TV Shows</string>
    <string name="home_popular_tv">Popular TV Shows</string>
    <string name="home_continue_watching">Continue Watching</string>
    
    <!-- Actions -->
    <string name="action_play">Play Now</string>
    <string name="action_download">Download</string>
    <string name="action_watch_later">Watch Later</string>
    <string name="action_add_watchlist">Add to Watchlist</string>
    <string name="action_remove_watchlist">Remove from Watchlist</string>
    <string name="action_share">Share</string>
    
    <!-- Search -->
    <string name="search_hint">Search movies and TV shows...</string>
    <string name="search_no_results">No results found</string>
    <string name="search_results_found">Found %d results for \"%s\"</string>
    
    <!-- Profile -->
    <string name="profile_sign_in">Sign In</string>
    <string name="profile_sign_out">Sign Out</string>
    <string name="profile_sign_up">Sign Up</string>
    <string name="profile_edit">Edit Profile</string>
    <string name="profile_watchlist">My Watchlist</string>
    <string name="profile_history">Watch History</string>
    <string name="profile_settings">Settings</string>
    
    <!-- Settings -->
    <string name="settings_streaming">Streaming Settings</string>
    <string name="settings_preferred_source">Preferred Source</string>
    <string name="settings_preferred_source_desc">Default streaming source</string>
    <string name="settings_preferred_quality">Preferred Quality</string>
    <string name="settings_preferred_quality_desc">Default video quality</string>
    <string name="settings_autoplay">Autoplay</string>
    <string name="settings_autoplay_desc">Automatically start playing videos</string>
    <string name="settings_subtitles">Subtitles</string>
    <string name="settings_subtitles_desc">Enable subtitles by default</string>
    <string name="settings_ad_block">Ad Block</string>
    <string name="settings_ad_block_desc">Block advertisements in video players</string>
    <string name="settings_privacy">Privacy &amp; Security</string>
    <string name="settings_clear_cache">Clear Cache</string>
    <string name="settings_clear_cache_desc">Clear app cache and temporary files</string>
    <string name="settings_privacy_policy">Privacy Policy</string>
    <string name="settings_privacy_policy_desc">View our privacy policy</string>
    <string name="settings_app">App Settings</string>
    <string name="settings_about">About</string>
    <string name="settings_about_desc">App version and information</string>
    <string name="settings_check_updates">Check for Updates</string>
    <string name="settings_check_updates_desc">Check for app updates</string>
    <string name="settings_reset">Reset Settings</string>
    <string name="settings_reset_desc">Reset all settings to default</string>
    
    <!-- Video Player -->
    <string name="player_loading">Loading...</string>
    <string name="player_error">Error loading video</string>
    <string name="player_retry">Retry</string>
    <string name="player_source">Source</string>
    <string name="player_quality">Quality</string>
    <string name="player_subtitles">Subtitles</string>
    <string name="player_fullscreen">Fullscreen</string>
    
    <!-- Quality Options -->
    <string name="quality_hd">HD</string>
    <string name="quality_cam">CAM</string>
    <string name="quality_720p">720p</string>
    <string name="quality_1080p">1080p</string>
    <string name="quality_4k">4K</string>
    
    <!-- Media Types -->
    <string name="media_type_movie">Movie</string>
    <string name="media_type_series">TV Series</string>
    <string name="media_type_episode">Episode</string>
    <string name="media_type_season">Season</string>
    
    <!-- Error Messages -->
    <string name="error_network">Network error. Please check your connection.</string>
    <string name="error_loading">Error loading content</string>
    <string name="error_not_found">Content not found</string>
    <string name="error_unauthorized">Please sign in to continue</string>
    <string name="error_server">Server error. Please try again later.</string>
    
    <!-- Loading States -->
    <string name="loading">Loading...</string>
    <string name="loading_movies">Loading movies...</string>
    <string name="loading_series">Loading TV shows...</string>
    <string name="loading_search">Searching...</string>
    
    <!-- Empty States -->
    <string name="empty_watchlist">Your watchlist is empty</string>
    <string name="empty_history">No watch history</string>
    <string name="empty_search">Search for movies and TV shows</string>
    
    <!-- Authentication -->
    <string name="auth_email">Email</string>
    <string name="auth_password">Password</string>
    <string name="auth_full_name">Full Name</string>
    <string name="auth_sign_in_title">Sign in to your account</string>
    <string name="auth_sign_up_title">Create new account</string>
    <string name="auth_forgot_password">Forgot Password?</string>
    <string name="auth_no_account">Don\'t have an account?</string>
    <string name="auth_have_account">Already have an account?</string>
    
    <!-- Success Messages -->
    <string name="success_added_watchlist">Added to watchlist</string>
    <string name="success_removed_watchlist">Removed from watchlist</string>
    <string name="success_profile_updated">Profile updated</string>
    <string name="success_settings_saved">Settings saved</string>
    
    <!-- Content Descriptions -->
    <string name="cd_movie_poster">Movie poster</string>
    <string name="cd_play_button">Play button</string>
    <string name="cd_download_button">Download button</string>
    <string name="cd_watchlist_button">Add to watchlist button</string>
    <string name="cd_back_button">Back button</string>
    <string name="cd_settings_button">Settings button</string>
    <string name="cd_search_button">Search button</string>
    <string name="cd_profile_avatar">Profile avatar</string>
    
    <!-- Footer -->
    <string name="footer_developed_by">Developed by XSHLABS</string>
    
    <!-- Version -->
    <string name="app_version">Version 1.0.0</string>
</resources>
