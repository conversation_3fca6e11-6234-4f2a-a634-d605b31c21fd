<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Android TV Required Features -->
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Cinemania"
        android:banner="@drawable/app_banner"
        tools:targetApi="31">

        <!-- Main TV Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/Theme.Cinemania"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Video Player Activity -->
        <activity
            android:name=".ui.player.VideoPlayerActivity"
            android:exported="false"
            android:theme="@style/Theme.Cinemania.Fullscreen"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />

        <!-- Details Activity -->
        <activity
            android:name=".ui.details.DetailsActivity"
            android:exported="false"
            android:theme="@style/Theme.Cinemania"
            android:screenOrientation="landscape" />

        <!-- Search Activity -->
        <activity
            android:name=".ui.search.SearchActivity"
            android:exported="false"
            android:theme="@style/Theme.Cinemania"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.SEARCH" />
            </intent-filter>
            <meta-data
                android:name="android.app.searchable"
                android:resource="@xml/searchable" />
        </activity>

    </application>

</manifest>
