package com.xshlabs.cinemania.data.repository

import com.xshlabs.cinemania.data.model.Profile
import com.xshlabs.cinemania.data.model.UserProfile
import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.auth.Auth
import io.github.jan.supabase.auth.providers.builtin.Email
import io.github.jan.supabase.auth.user.UserInfo
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.postgrest.query.Columns
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val supabaseClient: SupabaseClient
) {
    private val auth = supabaseClient.auth
    private val postgrest = supabaseClient.postgrest
    
    suspend fun signUp(email: String, password: String, fullName: String? = null): Result<UserInfo> = 
        withContext(Dispatchers.IO) {
            try {
                val result = auth.signUpWith(Email) {
                    this.email = email
                    this.password = password
                }
                
                // Create profile after successful signup
                result.user?.let { user ->
                    createProfile(user.id, email, fullName)
                }
                
                Result.success(result)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    
    suspend fun signIn(email: String, password: String): Result<UserInfo> = 
        withContext(Dispatchers.IO) {
            try {
                val result = auth.signInWith(Email) {
                    this.email = email
                    this.password = password
                }
                Result.success(result)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    
    suspend fun signOut(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            auth.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getCurrentUser(): UserProfile? = withContext(Dispatchers.IO) {
        try {
            val user = auth.currentUserOrNull()
            user?.let {
                val profile = getProfile(it.id)
                UserProfile(
                    id = it.id,
                    email = it.email ?: "",
                    fullName = profile?.fullName,
                    username = profile?.username,
                    avatarUrl = profile?.avatarUrl
                )
            }
        } catch (e: Exception) {
            null
        }
    }
    
    suspend fun getProfile(userId: String): Profile? = withContext(Dispatchers.IO) {
        try {
            val result = postgrest.from("profiles")
                .select(columns = Columns.list("id", "full_name", "username", "avatar_url", "created_at", "updated_at"))
                .eq("id", userId)
                .decodeSingle<Profile>()
            result
        } catch (e: Exception) {
            null
        }
    }
    
    private suspend fun createProfile(userId: String, email: String, fullName: String? = null) {
        try {
            val profile = mapOf(
                "id" to userId,
                "full_name" to fullName,
                "username" to email.substringBefore("@"),
                "created_at" to java.time.Instant.now().toString(),
                "updated_at" to java.time.Instant.now().toString()
            )
            
            postgrest.from("profiles").insert(profile)
        } catch (e: Exception) {
            // Log error but don't fail the signup process
            println("Failed to create profile: ${e.message}")
        }
    }
    
    suspend fun updateProfile(userId: String, updates: Map<String, Any?>): Result<Profile> = 
        withContext(Dispatchers.IO) {
            try {
                val updatedProfile = updates.toMutableMap()
                updatedProfile["updated_at"] = java.time.Instant.now().toString()
                
                val result = postgrest.from("profiles")
                    .update(updatedProfile)
                    .eq("id", userId)
                    .decodeSingle<Profile>()
                
                Result.success(result)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    
    suspend fun resetPassword(email: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            auth.resetPasswordForEmail(email)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    fun isUserLoggedIn(): Boolean {
        return auth.currentUserOrNull() != null
    }
    
    suspend fun refreshSession(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            auth.refreshCurrentSession()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
