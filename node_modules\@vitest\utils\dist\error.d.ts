import { D as DiffOptions } from './types.d-BCElaP-c.js';
import '@vitest/pretty-format';

// https://developer.mozilla.org/en-US/docs/Web/API/Web_Workers_API/Structured_clone_algorithm
declare function serializeValue(val: any, seen?: WeakMap<WeakKey, any>): any;

declare function processError(_err: any, diffOptions?: DiffOptions, seen?: WeakSet<WeakKey>): any;

export { processError, serializeValue as serializeError, serializeValue };
