package com.xshlabs.cinemania.data.repository

import com.xshlabs.cinemania.data.api.TMDBApi
import com.xshlabs.cinemania.data.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TMDBRepository @Inject constructor(
    private val api: TMDBApi
) {
    companion object {
        private const val API_KEY = "YOUR_TMDB_API_KEY" // Replace with actual API key
        private const val IMAGE_BASE_URL = "https://image.tmdb.org/t/p/"
        private const val POSTER_SIZE = "w500"
        private const val BACKDROP_SIZE = "w1280"
    }
    
    fun getImageUrl(path: String?, size: String = POSTER_SIZE): String {
        return if (path != null) {
            "$IMAGE_BASE_URL$size$path"
        } else {
            "" // Return empty string or placeholder URL
        }
    }
    
    suspend fun getTrendingMovies(): Result<List<MediaItem>> = withContext(Dispatchers.IO) {
        try {
            val response = api.getTrendingMovies(API_KEY)
            if (response.isSuccessful) {
                val movies = response.body()?.results?.take(20)?.map { movie ->
                    MediaItem(
                        id = movie.id,
                        title = movie.title,
                        year = movie.releaseDate?.take(4) ?: "2023",
                        rating = (movie.voteAverage * 10).toInt() / 10.0,
                        image = getImageUrl(movie.posterPath),
                        backdropImage = getImageUrl(movie.backdropPath, BACKDROP_SIZE),
                        quality = if (Math.random() > 0.3) "HD" else "CAM",
                        type = MediaType.MOVIE,
                        tmdbId = movie.id,
                        overview = movie.overview
                    )
                } ?: emptyList()
                Result.success(movies)
            } else {
                Result.failure(Exception("Failed to fetch trending movies: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getTrendingTVShows(): Result<List<MediaItem>> = withContext(Dispatchers.IO) {
        try {
            val response = api.getTrendingTVShows(API_KEY)
            if (response.isSuccessful) {
                val shows = response.body()?.results?.take(20)?.map { show ->
                    MediaItem(
                        id = show.id,
                        title = show.name,
                        year = show.firstAirDate?.take(4) ?: "2023",
                        rating = (show.voteAverage * 10).toInt() / 10.0,
                        image = getImageUrl(show.posterPath),
                        backdropImage = getImageUrl(show.backdropPath, BACKDROP_SIZE),
                        quality = if (Math.random() > 0.3) "HD" else "CAM",
                        type = MediaType.SERIES,
                        tmdbId = show.id,
                        overview = show.overview
                    )
                } ?: emptyList()
                Result.success(shows)
            } else {
                Result.failure(Exception("Failed to fetch trending TV shows: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getPopularMovies(): Result<List<MediaItem>> = withContext(Dispatchers.IO) {
        try {
            val response = api.getPopularMovies(API_KEY)
            if (response.isSuccessful) {
                val movies = response.body()?.results?.take(20)?.map { movie ->
                    MediaItem(
                        id = movie.id,
                        title = movie.title,
                        year = movie.releaseDate?.take(4) ?: "2023",
                        rating = (movie.voteAverage * 10).toInt() / 10.0,
                        image = getImageUrl(movie.posterPath),
                        backdropImage = getImageUrl(movie.backdropPath, BACKDROP_SIZE),
                        quality = if (Math.random() > 0.3) "HD" else "CAM",
                        type = MediaType.MOVIE,
                        tmdbId = movie.id,
                        overview = movie.overview
                    )
                } ?: emptyList()
                Result.success(movies)
            } else {
                Result.failure(Exception("Failed to fetch popular movies: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getPopularTVShows(): Result<List<MediaItem>> = withContext(Dispatchers.IO) {
        try {
            val response = api.getPopularTVShows(API_KEY)
            if (response.isSuccessful) {
                val shows = response.body()?.results?.take(20)?.map { show ->
                    MediaItem(
                        id = show.id,
                        title = show.name,
                        year = show.firstAirDate?.take(4) ?: "2023",
                        rating = (show.voteAverage * 10).toInt() / 10.0,
                        image = getImageUrl(show.posterPath),
                        backdropImage = getImageUrl(show.backdropPath, BACKDROP_SIZE),
                        quality = if (Math.random() > 0.3) "HD" else "CAM",
                        type = MediaType.SERIES,
                        tmdbId = show.id,
                        overview = show.overview
                    )
                } ?: emptyList()
                Result.success(shows)
            } else {
                Result.failure(Exception("Failed to fetch popular TV shows: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun searchMulti(query: String): Result<List<MediaItem>> = withContext(Dispatchers.IO) {
        try {
            val response = api.searchMulti(API_KEY, query)
            if (response.isSuccessful) {
                val results = response.body()?.results
                    ?.filter { it.mediaType == "movie" || it.mediaType == "tv" }
                    ?.take(20)
                    ?.map { item ->
                        MediaItem(
                            id = item.id,
                            title = if (item.mediaType == "movie") item.title ?: "Unknown" else item.name ?: "Unknown",
                            year = if (item.mediaType == "movie") 
                                item.releaseDate?.take(4) ?: "2023" 
                            else 
                                item.firstAirDate?.take(4) ?: "2023",
                            rating = (item.voteAverage * 10).toInt() / 10.0,
                            image = getImageUrl(item.posterPath),
                            backdropImage = getImageUrl(item.backdropPath, BACKDROP_SIZE),
                            quality = if (Math.random() > 0.3) "HD" else "CAM",
                            type = if (item.mediaType == "movie") MediaType.MOVIE else MediaType.SERIES,
                            tmdbId = item.id,
                            overview = item.overview ?: ""
                        )
                    } ?: emptyList()
                Result.success(results)
            } else {
                Result.failure(Exception("Failed to search: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getMovieDetails(movieId: Int): Result<MovieDetails> = withContext(Dispatchers.IO) {
        try {
            val response = api.getMovieDetails(movieId, API_KEY)
            if (response.isSuccessful) {
                response.body()?.let { Result.success(it) } 
                    ?: Result.failure(Exception("Movie details not found"))
            } else {
                Result.failure(Exception("Failed to fetch movie details: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getTVShowDetails(tvId: Int): Result<TVShowDetails> = withContext(Dispatchers.IO) {
        try {
            val response = api.getTVShowDetails(tvId, API_KEY)
            if (response.isSuccessful) {
                response.body()?.let { Result.success(it) } 
                    ?: Result.failure(Exception("TV show details not found"))
            } else {
                Result.failure(Exception("Failed to fetch TV show details: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getSimilarMovies(movieId: Int): Result<List<MediaItem>> = withContext(Dispatchers.IO) {
        try {
            val response = api.getSimilarMovies(movieId, API_KEY)
            if (response.isSuccessful) {
                val movies = response.body()?.results?.take(10)?.map { movie ->
                    MediaItem(
                        id = movie.id,
                        title = movie.title,
                        year = movie.releaseDate?.take(4) ?: "2023",
                        rating = (movie.voteAverage * 10).toInt() / 10.0,
                        image = getImageUrl(movie.posterPath),
                        backdropImage = getImageUrl(movie.backdropPath, BACKDROP_SIZE),
                        quality = if (Math.random() > 0.3) "HD" else "CAM",
                        type = MediaType.MOVIE,
                        tmdbId = movie.id,
                        overview = movie.overview
                    )
                } ?: emptyList()
                Result.success(movies)
            } else {
                Result.failure(Exception("Failed to fetch similar movies: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getSimilarTVShows(tvId: Int): Result<List<MediaItem>> = withContext(Dispatchers.IO) {
        try {
            val response = api.getSimilarTVShows(tvId, API_KEY)
            if (response.isSuccessful) {
                val shows = response.body()?.results?.take(10)?.map { show ->
                    MediaItem(
                        id = show.id,
                        title = show.name,
                        year = show.firstAirDate?.take(4) ?: "2023",
                        rating = (show.voteAverage * 10).toInt() / 10.0,
                        image = getImageUrl(show.posterPath),
                        backdropImage = getImageUrl(show.backdropPath, BACKDROP_SIZE),
                        quality = if (Math.random() > 0.3) "HD" else "CAM",
                        type = MediaType.SERIES,
                        tmdbId = show.id,
                        overview = show.overview
                    )
                } ?: emptyList()
                Result.success(shows)
            } else {
                Result.failure(Exception("Failed to fetch similar TV shows: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
