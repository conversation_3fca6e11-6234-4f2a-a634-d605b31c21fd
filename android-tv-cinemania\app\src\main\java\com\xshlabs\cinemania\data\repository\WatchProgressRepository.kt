package com.xshlabs.cinemania.data.repository

import com.xshlabs.cinemania.data.model.ContinueWatchingItem
import com.xshlabs.cinemania.data.model.MediaType
import com.xshlabs.cinemania.data.model.WatchProgress
import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.postgrest.query.Columns
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WatchProgressRepository @Inject constructor(
    private val supabaseClient: SupabaseClient
) {
    private val postgrest = supabaseClient.postgrest
    
    suspend fun saveWatchProgress(
        userId: String,
        tmdbId: Int,
        mediaType: MediaType,
        title: String,
        posterPath: String?,
        backdropPath: String?,
        releaseDate: String?,
        voteAverage: Double?,
        imdbId: String?,
        currentTime: Long,
        totalDuration: Long,
        seasonNumber: Int? = null,
        episodeNumber: Int? = null
    ): Result<WatchProgress> = withContext(Dispatchers.IO) {
        try {
            val progressPercentage = if (totalDuration > 0) {
                (currentTime.toDouble() / totalDuration.toDouble()) * 100
            } else 0.0
            
            val isCompleted = progressPercentage >= 90.0 // Consider 90%+ as completed
            
            val watchProgressData = mapOf(
                "user_id" to userId,
                "tmdb_id" to tmdbId,
                "media_type" to mediaType.name.lowercase(),
                "title" to title,
                "poster_path" to posterPath,
                "backdrop_path" to backdropPath,
                "release_date" to releaseDate,
                "vote_average" to voteAverage,
                "imdb_id" to imdbId,
                "current_time" to currentTime,
                "total_duration" to totalDuration,
                "progress_percentage" to progressPercentage,
                "season_number" to seasonNumber,
                "episode_number" to episodeNumber,
                "is_completed" to isCompleted,
                "last_watched" to java.time.Instant.now().toString(),
                "updated_at" to java.time.Instant.now().toString()
            )
            
            // Check if progress already exists
            val existingProgress = getWatchProgress(userId, tmdbId, mediaType, seasonNumber, episodeNumber)
            
            val result = if (existingProgress != null) {
                // Update existing progress
                postgrest.from("watch_progress")
                    .update(watchProgressData)
                    .eq("user_id", userId)
                    .eq("tmdb_id", tmdbId)
                    .eq("media_type", mediaType.name.lowercase())
                    .apply {
                        if (seasonNumber != null) eq("season_number", seasonNumber)
                        if (episodeNumber != null) eq("episode_number", episodeNumber)
                    }
                    .decodeSingle<WatchProgress>()
            } else {
                // Insert new progress
                val newData = watchProgressData.toMutableMap()
                newData["created_at"] = java.time.Instant.now().toString()
                
                postgrest.from("watch_progress")
                    .insert(newData)
                    .decodeSingle<WatchProgress>()
            }
            
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getWatchProgress(
        userId: String,
        tmdbId: Int,
        mediaType: MediaType,
        seasonNumber: Int? = null,
        episodeNumber: Int? = null
    ): WatchProgress? = withContext(Dispatchers.IO) {
        try {
            var query = postgrest.from("watch_progress")
                .select()
                .eq("user_id", userId)
                .eq("tmdb_id", tmdbId)
                .eq("media_type", mediaType.name.lowercase())
            
            if (seasonNumber != null) {
                query = query.eq("season_number", seasonNumber)
            }
            if (episodeNumber != null) {
                query = query.eq("episode_number", episodeNumber)
            }
            
            query.decodeSingle<WatchProgress>()
        } catch (e: Exception) {
            null
        }
    }
    
    suspend fun getContinueWatching(userId: String): Result<List<ContinueWatchingItem>> = 
        withContext(Dispatchers.IO) {
            try {
                val progressList = postgrest.from("watch_progress")
                    .select()
                    .eq("user_id", userId)
                    .eq("is_completed", false)
                    .order("last_watched", ascending = false)
                    .limit(20)
                    .decodeList<WatchProgress>()
                
                val continueWatchingItems = progressList.map { progress ->
                    ContinueWatchingItem(
                        tmdbId = progress.tmdbId,
                        mediaType = if (progress.mediaType == "movie") MediaType.MOVIE else MediaType.SERIES,
                        title = progress.title ?: "Unknown Title",
                        posterPath = progress.posterPath,
                        backdropPath = progress.backdropPath,
                        progressPercentage = progress.progressPercentage,
                        lastWatched = progress.lastWatched,
                        seasonNumber = progress.seasonNumber,
                        episodeNumber = progress.episodeNumber
                    )
                }
                
                Result.success(continueWatchingItems)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    
    suspend fun getWatchedHistory(userId: String): Result<List<WatchProgress>> = 
        withContext(Dispatchers.IO) {
            try {
                val history = postgrest.from("watch_progress")
                    .select()
                    .eq("user_id", userId)
                    .order("last_watched", ascending = false)
                    .limit(50)
                    .decodeList<WatchProgress>()
                
                Result.success(history)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    
    suspend fun removeWatchProgress(
        userId: String,
        tmdbId: Int,
        mediaType: MediaType,
        seasonNumber: Int? = null,
        episodeNumber: Int? = null
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            var query = postgrest.from("watch_progress")
                .delete()
                .eq("user_id", userId)
                .eq("tmdb_id", tmdbId)
                .eq("media_type", mediaType.name.lowercase())
            
            if (seasonNumber != null) {
                query = query.eq("season_number", seasonNumber)
            }
            if (episodeNumber != null) {
                query = query.eq("episode_number", episodeNumber)
            }
            
            query.execute()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun markAsCompleted(
        userId: String,
        tmdbId: Int,
        mediaType: MediaType,
        seasonNumber: Int? = null,
        episodeNumber: Int? = null
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val updateData = mapOf(
                "is_completed" to true,
                "progress_percentage" to 100.0,
                "updated_at" to java.time.Instant.now().toString()
            )
            
            var query = postgrest.from("watch_progress")
                .update(updateData)
                .eq("user_id", userId)
                .eq("tmdb_id", tmdbId)
                .eq("media_type", mediaType.name.lowercase())
            
            if (seasonNumber != null) {
                query = query.eq("season_number", seasonNumber)
            }
            if (episodeNumber != null) {
                query = query.eq("episode_number", episodeNumber)
            }
            
            query.execute()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun clearWatchHistory(userId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            postgrest.from("watch_progress")
                .delete()
                .eq("user_id", userId)
                .execute()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getProgressPercentage(
        userId: String,
        tmdbId: Int,
        mediaType: MediaType,
        seasonNumber: Int? = null,
        episodeNumber: Int? = null
    ): Double {
        return try {
            val progress = getWatchProgress(userId, tmdbId, mediaType, seasonNumber, episodeNumber)
            progress?.progressPercentage ?: 0.0
        } catch (e: Exception) {
            0.0
        }
    }
    
    suspend fun isCompleted(
        userId: String,
        tmdbId: Int,
        mediaType: MediaType,
        seasonNumber: Int? = null,
        episodeNumber: Int? = null
    ): Boolean {
        return try {
            val progress = getWatchProgress(userId, tmdbId, mediaType, seasonNumber, episodeNumber)
            progress?.isCompleted ?: false
        } catch (e: Exception) {
            false
        }
    }
}
