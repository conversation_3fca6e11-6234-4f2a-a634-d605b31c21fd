package com.xshlabs.cinemania.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xshlabs.cinemania.ui.theme.CinemaniaRed

@Composable
fun SettingsScreen() {
    var preferredSource by remember { mutableStateOf("vidsrc") }
    var adBlockEnabled by remember { mutableStateOf(true) }
    var autoplayEnabled by remember { mutableStateOf(true) }
    var subtitlesEnabled by remember { mutableStateOf(true) }
    var preferredQuality by remember { mutableStateOf("1080p") }
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        item {
            Text(
                text = "Settings",
                color = Color.White,
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }
        
        // Streaming Settings
        item {
            SettingsSection(title = "Streaming Settings") {
                // Preferred Source
                SettingsDropdown(
                    title = "Preferred Source",
                    subtitle = "Default streaming source",
                    value = preferredSource,
                    options = listOf("vidsrc", "embedsu", "multiembed", "smashystream"),
                    onValueChange = { preferredSource = it }
                )
                
                // Preferred Quality
                SettingsDropdown(
                    title = "Preferred Quality",
                    subtitle = "Default video quality",
                    value = preferredQuality,
                    options = listOf("720p", "1080p", "4K"),
                    onValueChange = { preferredQuality = it }
                )
                
                // Autoplay
                SettingsSwitch(
                    title = "Autoplay",
                    subtitle = "Automatically start playing videos",
                    checked = autoplayEnabled,
                    onCheckedChange = { autoplayEnabled = it }
                )
                
                // Subtitles
                SettingsSwitch(
                    title = "Subtitles",
                    subtitle = "Enable subtitles by default",
                    checked = subtitlesEnabled,
                    onCheckedChange = { subtitlesEnabled = it }
                )
            }
        }
        
        // Privacy Settings
        item {
            SettingsSection(title = "Privacy & Security") {
                // Ad Block
                SettingsSwitch(
                    title = "Ad Block",
                    subtitle = "Block advertisements in video players",
                    checked = adBlockEnabled,
                    onCheckedChange = { adBlockEnabled = it }
                )
                
                // Clear Cache
                SettingsAction(
                    title = "Clear Cache",
                    subtitle = "Clear app cache and temporary files",
                    icon = Icons.Default.Delete,
                    onClick = { /* Handle clear cache */ }
                )
                
                // Privacy Policy
                SettingsAction(
                    title = "Privacy Policy",
                    subtitle = "View our privacy policy",
                    icon = Icons.Default.Security,
                    onClick = { /* Handle privacy policy */ }
                )
            }
        }
        
        // App Settings
        item {
            SettingsSection(title = "App Settings") {
                // About
                SettingsAction(
                    title = "About",
                    subtitle = "App version and information",
                    icon = Icons.Default.Info,
                    onClick = { /* Handle about */ }
                )
                
                // Check for Updates
                SettingsAction(
                    title = "Check for Updates",
                    subtitle = "Check for app updates",
                    icon = Icons.Default.Update,
                    onClick = { /* Handle check updates */ }
                )
                
                // Reset Settings
                SettingsAction(
                    title = "Reset Settings",
                    subtitle = "Reset all settings to default",
                    icon = Icons.Default.RestartAlt,
                    onClick = { /* Handle reset */ }
                )
            }
        }
    }
}

@Composable
fun SettingsSection(
    title: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = title,
                color = CinemaniaRed,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            content()
        }
    }
}

@Composable
fun SettingsSwitch(
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    var isFocused by remember { mutableStateOf(false) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onCheckedChange(!checked) }
            .focusable()
            .onFocusChanged { isFocused = it.isFocused }
            .background(
                color = if (isFocused) CinemaniaRed.copy(alpha = 0.2f) else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .border(
                width = if (isFocused) 2.dp else 0.dp,
                color = if (isFocused) CinemaniaRed else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                color = Color.Gray,
                fontSize = 14.sp
            )
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = CinemaniaRed,
                uncheckedThumbColor = Color.Gray,
                uncheckedTrackColor = Color.DarkGray
            )
        )
    }
}

@Composable
fun SettingsDropdown(
    title: String,
    subtitle: String,
    value: String,
    options: List<String>,
    onValueChange: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    var isFocused by remember { mutableStateOf(false) }
    
    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .focusable()
                .onFocusChanged { isFocused = it.isFocused }
                .background(
                    color = if (isFocused) CinemaniaRed.copy(alpha = 0.2f) else Color.Transparent,
                    shape = RoundedCornerShape(8.dp)
                )
                .border(
                    width = if (isFocused) 2.dp else 0.dp,
                    color = if (isFocused) CinemaniaRed else Color.Transparent,
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = subtitle,
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            }
            
            Text(
                text = value.uppercase(),
                color = CinemaniaRed,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold
            )
            
            Icon(
                imageVector = Icons.Default.ArrowDropDown,
                contentDescription = "Dropdown",
                tint = Color.Gray
            )
        }
        
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier.background(Color.Black.copy(alpha = 0.9f))
        ) {
            options.forEach { option ->
                DropdownMenuItem(
                    text = {
                        Text(
                            text = option.uppercase(),
                            color = if (option == value) CinemaniaRed else Color.White
                        )
                    },
                    onClick = {
                        onValueChange(option)
                        expanded = false
                    }
                )
            }
        }
    }
}

@Composable
fun SettingsAction(
    title: String,
    subtitle: String,
    icon: ImageVector,
    onClick: () -> Unit
) {
    var isFocused by remember { mutableStateOf(false) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .focusable()
            .onFocusChanged { isFocused = it.isFocused }
            .background(
                color = if (isFocused) CinemaniaRed.copy(alpha = 0.2f) else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .border(
                width = if (isFocused) 2.dp else 0.dp,
                color = if (isFocused) CinemaniaRed else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = CinemaniaRed,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                color = Color.Gray,
                fontSize = 14.sp
            )
        }
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = "Navigate",
            tint = Color.Gray,
            modifier = Modifier.size(20.dp)
        )
    }
}
