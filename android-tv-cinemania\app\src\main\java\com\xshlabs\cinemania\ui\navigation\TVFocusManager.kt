package com.xshlabs.cinemania.ui.navigation

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.*
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalFocusManager
import kotlinx.coroutines.delay

/**
 * TV Focus Manager for handling D-pad navigation and focus management
 * Optimized for Android TV remote control navigation
 */
@Composable
fun TVFocusManager(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val focusManager = LocalFocusManager.current
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .onKeyEvent { keyEvent ->
                handleTVKeyEvent(keyEvent, focusManager)
            }
            .focusable()
    ) {
        content()
    }
}

private fun handleTVKeyEvent(
    keyEvent: KeyEvent,
    focusManager: FocusManager
): Boolean {
    if (keyEvent.type != KeyEventType.KeyDown) return false
    
    return when (keyEvent.key) {
        Key.DirectionUp -> {
            focusManager.moveFocus(FocusDirection.Up)
            true
        }
        Key.DirectionDown -> {
            focusManager.moveFocus(FocusDirection.Down)
            true
        }
        Key.DirectionLeft -> {
            focusManager.moveFocus(FocusDirection.Left)
            true
        }
        Key.DirectionRight -> {
            focusManager.moveFocus(FocusDirection.Right)
            true
        }
        Key.DirectionCenter,
        Key.Enter,
        Key.NumPadEnter -> {
            // Handle center/enter key press
            true
        }
        Key.Back,
        Key.Escape -> {
            // Handle back key press
            true
        }
        else -> false
    }
}

/**
 * Modifier for TV-optimized focusable elements
 */
fun Modifier.tvFocusable(
    enabled: Boolean = true,
    onFocusChanged: ((FocusState) -> Unit)? = null
): Modifier = this.then(
    if (enabled) {
        Modifier
            .focusable()
            .onFocusChanged { focusState ->
                onFocusChanged?.invoke(focusState)
            }
    } else {
        Modifier
    }
)

/**
 * Focus requester for TV navigation
 */
@Composable
fun rememberTVFocusRequester(): FocusRequester {
    return remember { FocusRequester() }
}

/**
 * Auto-focus effect for TV navigation
 * Automatically focuses the first focusable element when the screen loads
 */
@Composable
fun TVAutoFocus(
    focusRequester: FocusRequester,
    delay: Long = 100L
) {
    LaunchedEffect(focusRequester) {
        delay(delay)
        try {
            focusRequester.requestFocus()
        } catch (e: Exception) {
            // Ignore focus request failures
        }
    }
}

/**
 * Focus group for managing focus within a specific area
 */
@Composable
fun TVFocusGroup(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier.focusGroup()
    ) {
        content()
    }
}

/**
 * Focus restoration for maintaining focus state
 */
@Composable
fun rememberTVFocusState(): MutableState<Boolean> {
    return remember { mutableStateOf(false) }
}

/**
 * TV-specific focus properties
 */
object TVFocusProperties {
    const val FOCUS_BORDER_WIDTH = 3
    const val FOCUS_ANIMATION_DURATION = 150
    const val FOCUS_SCALE_FACTOR = 1.05f
    const val AUTO_FOCUS_DELAY = 100L
}

/**
 * Focus direction utilities for TV navigation
 */
object TVFocusDirection {
    fun getOpposite(direction: FocusDirection): FocusDirection {
        return when (direction) {
            FocusDirection.Up -> FocusDirection.Down
            FocusDirection.Down -> FocusDirection.Up
            FocusDirection.Left -> FocusDirection.Right
            FocusDirection.Right -> FocusDirection.Left
            FocusDirection.In -> FocusDirection.Out
            FocusDirection.Out -> FocusDirection.In
            else -> direction
        }
    }
    
    fun isHorizontal(direction: FocusDirection): Boolean {
        return direction == FocusDirection.Left || direction == FocusDirection.Right
    }
    
    fun isVertical(direction: FocusDirection): Boolean {
        return direction == FocusDirection.Up || direction == FocusDirection.Down
    }
}

/**
 * TV Focus behavior configuration
 */
data class TVFocusConfig(
    val enableAutoFocus: Boolean = true,
    val autoFocusDelay: Long = TVFocusProperties.AUTO_FOCUS_DELAY,
    val enableFocusSound: Boolean = true,
    val enableFocusAnimation: Boolean = true,
    val focusAnimationDuration: Int = TVFocusProperties.FOCUS_ANIMATION_DURATION,
    val focusScaleFactor: Float = TVFocusProperties.FOCUS_SCALE_FACTOR
)

/**
 * Default TV focus configuration
 */
val DefaultTVFocusConfig = TVFocusConfig()

/**
 * Focus state management for TV navigation
 */
@Stable
class TVFocusState {
    private var _currentFocusedItem by mutableStateOf<String?>(null)
    private var _focusHistory = mutableListOf<String>()
    
    val currentFocusedItem: String? get() = _currentFocusedItem
    val focusHistory: List<String> get() = _focusHistory.toList()
    
    fun setFocusedItem(itemId: String) {
        _currentFocusedItem = itemId
        _focusHistory.add(itemId)
        
        // Keep only last 10 items in history
        if (_focusHistory.size > 10) {
            _focusHistory.removeAt(0)
        }
    }
    
    fun clearFocus() {
        _currentFocusedItem = null
    }
    
    fun getPreviousFocusedItem(): String? {
        return if (_focusHistory.size >= 2) {
            _focusHistory[_focusHistory.size - 2]
        } else null
    }
}

/**
 * Remember TV focus state
 */
@Composable
fun rememberTVFocusState(): TVFocusState {
    return remember { TVFocusState() }
}
