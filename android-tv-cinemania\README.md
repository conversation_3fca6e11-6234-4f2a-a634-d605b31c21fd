# Cinemania Android TV App

A comprehensive Android TV streaming application that provides access to movies and TV shows with a user-friendly interface optimized for TV remote control navigation.

## Features

- **Android TV Optimized**: Built specifically for Android TV with D-pad navigation support
- **Movie & TV Show Streaming**: Access to a vast library of movies and TV series
- **Multiple Streaming Sources**: Support for various streaming providers (VidSrc, EmbedSu, MultiEmbed, SmashyStream)
- **User Authentication**: Secure login and profile management with Supabase
- **Watch Progress Tracking**: Continue watching from where you left off
- **Watchlist Management**: Save movies and shows for later viewing
- **Search Functionality**: Find content quickly with built-in search
- **High-Quality Streaming**: Support for HD and 4K content
- **Dark Theme**: Optimized for TV viewing with a sleek dark interface

## Screenshots

*Screenshots will be added after the app is built and tested*

## Requirements

### Development Requirements
- Android Studio Arctic Fox or later
- JDK 11 or higher
- Android SDK API 21+ (Android 5.0)
- Kotlin 1.9+

### Runtime Requirements
- Android TV device running Android 5.0 (API 21) or higher
- Internet connection for streaming content
- Minimum 2GB RAM recommended
- 100MB free storage space

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <your-repo-url>
cd android-tv-cinemania
```

### 2. Configure API Keys

Before building the app, you need to configure the following API keys:

#### TMDB API Key
1. Go to [The Movie Database (TMDB)](https://www.themoviedb.org/settings/api)
2. Create an account and request an API key
3. Open `app/src/main/java/com/xshlabs/cinemania/data/repository/TMDBRepository.kt`
4. Replace `YOUR_TMDB_API_KEY` with your actual TMDB API key

#### Supabase Configuration
1. Go to [Supabase](https://supabase.com) and create a new project
2. Get your project URL and anon key from the project settings
3. Open `app/src/main/java/com/xshlabs/cinemania/di/NetworkModule.kt`
4. Replace `YOUR_SUPABASE_URL` with your Supabase project URL
5. Replace `YOUR_SUPABASE_ANON_KEY` with your Supabase anon key

#### Database Setup
1. In your Supabase project, run the following SQL to create the required tables:

```sql
-- Create profiles table
CREATE TABLE profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE,
    full_name TEXT,
    username TEXT UNIQUE,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (id)
);

-- Create watchlist table
CREATE TABLE watchlist (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE,
    tmdb_id INTEGER NOT NULL,
    media_type TEXT NOT NULL CHECK (media_type IN ('movie', 'tv')),
    title TEXT,
    poster_path TEXT,
    backdrop_path TEXT,
    release_date TEXT,
    vote_average DECIMAL,
    imdb_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create watch_progress table
CREATE TABLE watch_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE,
    tmdb_id INTEGER NOT NULL,
    media_type TEXT NOT NULL CHECK (media_type IN ('movie', 'tv')),
    title TEXT,
    poster_path TEXT,
    backdrop_path TEXT,
    release_date TEXT,
    vote_average DECIMAL,
    imdb_id TEXT,
    current_time BIGINT NOT NULL DEFAULT 0,
    total_duration BIGINT NOT NULL DEFAULT 0,
    progress_percentage DECIMAL NOT NULL DEFAULT 0,
    season_number INTEGER,
    episode_number INTEGER,
    is_completed BOOLEAN DEFAULT FALSE,
    last_watched TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE watchlist ENABLE ROW LEVEL SECURITY;
ALTER TABLE watch_progress ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own watchlist" ON watchlist FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own watchlist" ON watchlist FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own watchlist" ON watchlist FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own watchlist" ON watchlist FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own progress" ON watch_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own progress" ON watch_progress FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own progress" ON watch_progress FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own progress" ON watch_progress FOR DELETE USING (auth.uid() = user_id);
```

### 3. Build the App

#### Using Android Studio
1. Open Android Studio
2. Click "Open an existing Android Studio project"
3. Navigate to the `android-tv-cinemania` folder and select it
4. Wait for Gradle sync to complete
5. Click "Build" > "Build Bundle(s) / APK(s)" > "Build APK(s)"
6. The APK will be generated in `app/build/outputs/apk/debug/`

#### Using Command Line
```bash
# Navigate to project directory
cd android-tv-cinemania

# Build debug APK
./gradlew assembleDebug

# Build release APK (requires signing configuration)
./gradlew assembleRelease
```

## Installation Guide

### Method 1: Direct Installation (Recommended)

1. **Enable Developer Options on your Android TV:**
   - Go to Settings > Device Preferences > About
   - Click on "Build" 7 times until you see "You are now a developer"

2. **Enable USB Debugging:**
   - Go to Settings > Device Preferences > Developer Options
   - Enable "USB debugging"

3. **Install via ADB:**
   ```bash
   # Connect your Android TV to your computer via USB
   # Install the APK
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

### Method 2: Sideloading with File Manager

1. **Copy APK to USB Drive:**
   - Copy the generated APK file to a USB drive

2. **Install File Manager on Android TV:**
   - Install "X-plore File Manager" or similar from Google Play Store

3. **Install APK:**
   - Insert USB drive into Android TV
   - Open File Manager
   - Navigate to USB drive and find the APK file
   - Click on the APK file and select "Install"

### Method 3: Wireless Installation

1. **Enable ADB over Network:**
   - Go to Settings > Device Preferences > Developer Options
   - Enable "Network debugging"
   - Note the IP address shown

2. **Connect via ADB:**
   ```bash
   # Replace IP_ADDRESS with your Android TV's IP
   adb connect IP_ADDRESS:5555
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

## Usage

1. **Launch the App:**
   - Find "Cinemania" in your Android TV apps
   - Launch the application

2. **Navigation:**
   - Use D-pad on your TV remote to navigate
   - Press OK/Enter to select items
   - Press Back to go back

3. **First Time Setup:**
   - Create an account or sign in
   - Browse movies and TV shows
   - Add items to your watchlist
   - Start streaming!

## Troubleshooting

### Common Issues

**App won't install:**
- Make sure "Unknown sources" is enabled in Settings > Security & Restrictions
- Check that you have enough storage space
- Verify the APK file is not corrupted

**Streaming not working:**
- Check your internet connection
- Try switching to a different streaming source in settings
- Ensure your device supports the video format

**Focus/Navigation issues:**
- Make sure you're using a compatible TV remote
- Try restarting the app
- Check if your Android TV system is up to date

### Getting Help

If you encounter issues:
1. Check the troubleshooting section above
2. Restart the app and try again
3. Restart your Android TV device
4. Check for app updates

## Development

### Project Structure
```
android-tv-cinemania/
├── app/
│   ├── src/main/java/com/xshlabs/cinemania/
│   │   ├── data/           # Data layer (models, repositories, API)
│   │   ├── di/             # Dependency injection
│   │   ├── ui/             # UI layer (screens, components, themes)
│   │   └── MainActivity.kt # Main activity
│   └── build.gradle.kts    # App-level build configuration
├── gradle/                 # Gradle wrapper and version catalog
└── build.gradle.kts       # Project-level build configuration
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on Android TV
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This app is for educational purposes only. Users are responsible for ensuring they have the right to access and stream content through the provided sources. The developers are not responsible for any copyright infringement or illegal use of the application.

## Credits

- **TMDB**: Movie and TV show data provided by The Movie Database
- **Supabase**: Backend services and authentication
- **Streaming Sources**: Various third-party streaming providers
- **Icons**: Material Design Icons
- **UI Framework**: Jetpack Compose for TV

---

**Developed by XSHLABS**
