package com.xshlabs.cinemania.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xshlabs.cinemania.data.model.MediaItem
import com.xshlabs.cinemania.data.model.MediaType
import com.xshlabs.cinemania.ui.components.MovieCard

@Composable
fun SeriesScreen() {
    val sampleSeries = remember {
        List(50) { index ->
            MediaItem(
                id = index,
                title = "TV Series ${index + 1}",
                year = (2020 + (index % 4)).toString(),
                rating = 6.0 + (index % 4),
                image = "",
                backdropImage = "",
                quality = if (index % 3 == 0) "CAM" else "HD",
                type = MediaType.SERIES,
                tmdbId = index,
                overview = "This is a sample TV series description for series ${index + 1}."
            )
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Text(
            text = "TV Series",
            color = Color.White,
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // Series Grid
        LazyVerticalGrid(
            columns = GridCells.Adaptive(minSize = 160.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(bottom = 32.dp)
        ) {
            items(sampleSeries) { series ->
                MovieCard(
                    mediaItem = series,
                    onClick = { /* Handle series click */ }
                )
            }
        }
    }
}
