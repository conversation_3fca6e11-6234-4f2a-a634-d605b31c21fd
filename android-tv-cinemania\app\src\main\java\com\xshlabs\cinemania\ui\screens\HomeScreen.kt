package com.xshlabs.cinemania.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xshlabs.cinemania.data.model.MediaItem
import com.xshlabs.cinemania.ui.components.HeroSection
import com.xshlabs.cinemania.ui.components.MovieCard

@Composable
fun HomeScreen() {
    // Mock data for now - will be replaced with ViewModel later
    val featuredMovie = remember {
        MediaItem(
            id = 1,
            title = "Sample Movie",
            year = "2023",
            rating = 8.5,
            image = "",
            backdropImage = "",
            quality = "HD",
            type = com.xshlabs.cinemania.data.model.MediaType.MOVIE,
            tmdbId = 1,
            overview = "This is a sample movie description for the featured content."
        )
    }

    val sampleMovies = remember {
        List(10) { index ->
            MediaItem(
                id = index,
                title = "Movie $index",
                year = "2023",
                rating = 7.0 + (index % 3),
                image = "",
                backdropImage = "",
                quality = if (index % 2 == 0) "HD" else "CAM",
                type = com.xshlabs.cinemania.data.model.MediaType.MOVIE,
                tmdbId = index,
                overview = "Sample movie description"
            )
        }
    }
    
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // Hero Section
        item {
            HeroSection(
                featuredItem = featuredMovie,
                onPlayClick = { /* Handle play */ },
                onDownloadClick = { /* Handle download */ },
                onWatchLaterClick = { /* Handle watch later */ }
            )
        }

        // Trending Movies
        item {
            MediaSection(
                title = "Trending Movies",
                items = sampleMovies,
                onItemClick = { /* Handle item click */ }
            )
        }

        // Popular Movies
        item {
            MediaSection(
                title = "Popular Movies",
                items = sampleMovies,
                onItemClick = { /* Handle item click */ }
            )
        }

        // Trending TV Shows
        item {
            MediaSection(
                title = "Trending TV Shows",
                items = sampleMovies.map { it.copy(type = com.xshlabs.cinemania.data.model.MediaType.SERIES) },
                onItemClick = { /* Handle item click */ }
            )
        }

        // Bottom spacing
        item {
            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

@Composable
fun MediaSection(
    title: String,
    items: List<MediaItem>,
    onItemClick: (MediaItem) -> Unit,
    showProgress: Boolean = false,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(horizontal = 16.dp)
    ) {
        Text(
            text = title,
            color = Color.White,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(items) { item ->
                MovieCard(
                    mediaItem = item,
                    onClick = { onItemClick(item) },
                    showProgress = showProgress,
                    progressPercentage = if (showProgress) (Math.random() * 100).toFloat() else 0f
                )
            }
        }
    }
}
