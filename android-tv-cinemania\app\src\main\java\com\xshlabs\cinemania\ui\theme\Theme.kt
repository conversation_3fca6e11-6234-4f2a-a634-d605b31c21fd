package com.xshlabs.cinemania.ui.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

private val DarkColorScheme = darkColorScheme(
    primary = CinemaniaRed,
    onPrimary = CinemaniaWhite,
    primaryContainer = CinemaniaRedTransparent45,
    onPrimaryContainer = CinemaniaWhite,
    
    secondary = DarkGray,
    onSecondary = CinemaniaWhite,
    secondaryContainer = MediumGray,
    onSecondaryContainer = CinemaniaWhite,
    
    tertiary = LightGray,
    onTertiary = CinemaniaBlack,
    tertiaryContainer = DarkGray,
    onTertiaryContainer = CinemaniaWhite,
    
    background = DarkBackground,
    onBackground = DarkOnBackground,
    
    surface = DarkSurface,
    onSurface = DarkOnSurface,
    surfaceVariant = DarkCard,
    onSurfaceVariant = CinemaniaWhite,
    
    error = ErrorRed,
    onError = CinemaniaWhite,
    errorContainer = Color(0xFF93000A),
    onErrorContainer = CinemaniaWhite,
    
    outline = MediumGray,
    outlineVariant = DarkGray,
    
    scrim = Color.Black.copy(alpha = 0.5f),
    
    inverseSurface = CinemaniaWhite,
    inverseOnSurface = CinemaniaBlack,
    inversePrimary = CinemaniaRed,
    
    surfaceDim = Color(0xFF111111),
    surfaceBright = Color(0xFF333333),
    surfaceContainerLowest = Color(0xFF0A0A0A),
    surfaceContainerLow = Color(0xFF191919),
    surfaceContainer = Color(0xFF1D1D1D),
    surfaceContainerHigh = Color(0xFF272727),
    surfaceContainerHighest = Color(0xFF323232)
)

@Composable
fun CinemaniaTheme(
    content: @Composable () -> Unit
) {
    MaterialTheme(
        colorScheme = DarkColorScheme,
        typography = Typography,
        content = content
    )
}
