package com.xshlabs.cinemania.ui.theme

import androidx.compose.ui.graphics.Color

// XShtreams Color Palette
val CinemaniaRed = Color(0xFFDF2531)
val CinemaniaRedTransparent45 = Color(0x73DF2531)
val CinemaniaRedTransparent65 = Color(0xA6DF2531)
val CinemaniaWhite = Color(0xFFFFFFFF)
val CinemaniaBlack = Color(0xFF000000)

// Dark Theme Colors
val DarkBackground = CinemaniaBlack
val DarkSurface = Color(0xFF0D0D0D) // Slightly lighter than pure black
val DarkCard = Color(0xFF1A1A1A)
val DarkOnSurface = CinemaniaWhite
val DarkOnBackground = CinemaniaWhite

// Secondary Colors
val DarkGray = Color(0xFF262626)
val MediumGray = Color(0xFF404040)
val LightGray = Color(0xFF808080)

// Status Colors
val SuccessGreen = Color(0xFF4CAF50)
val WarningOrange = Color(0xFFFF9800)
val ErrorRed = Color(0xFFF44336)

// Focus and Selection
val FocusBorder = CinemaniaRed
val SelectionBackground = CinemaniaRedTransparent45
