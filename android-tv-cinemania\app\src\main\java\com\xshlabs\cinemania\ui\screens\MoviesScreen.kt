package com.xshlabs.cinemania.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xshlabs.cinemania.data.model.MediaItem
import com.xshlabs.cinemania.data.model.MediaType
import com.xshlabs.cinemania.ui.components.MovieCard

@Composable
fun MoviesScreen() {
    val sampleMovies = remember {
        List(50) { index ->
            MediaItem(
                id = index,
                title = "Movie ${index + 1}",
                year = (2020 + (index % 4)).toString(),
                rating = 6.0 + (index % 4),
                image = "",
                backdropImage = "",
                quality = if (index % 3 == 0) "CAM" else "HD",
                type = MediaType.MOVIE,
                tmdbId = index,
                overview = "This is a sample movie description for movie ${index + 1}."
            )
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Text(
            text = "Movies",
            color = Color.White,
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // Movies Grid
        LazyVerticalGrid(
            columns = GridCells.Adaptive(minSize = 160.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(bottom = 32.dp)
        ) {
            items(sampleMovies) { movie ->
                MovieCard(
                    mediaItem = movie,
                    onClick = { /* Handle movie click */ }
                )
            }
        }
    }
}
