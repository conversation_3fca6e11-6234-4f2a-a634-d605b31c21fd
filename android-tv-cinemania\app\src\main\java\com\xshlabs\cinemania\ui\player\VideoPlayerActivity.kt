package com.xshlabs.cinemania.ui.player

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.xshlabs.cinemania.data.model.MediaType
import com.xshlabs.cinemania.ui.theme.CinemaniaTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay

@AndroidEntryPoint
class VideoPlayerActivity : ComponentActivity() {
    
    private var exoPlayer: ExoPlayer? = null
    
    companion object {
        private const val EXTRA_TITLE = "extra_title"
        private const val EXTRA_TMDB_ID = "extra_tmdb_id"
        private const val EXTRA_IMDB_ID = "extra_imdb_id"
        private const val EXTRA_MEDIA_TYPE = "extra_media_type"
        private const val EXTRA_SEASON = "extra_season"
        private const val EXTRA_EPISODE = "extra_episode"
        
        fun createIntent(
            context: Context,
            title: String,
            tmdbId: Int,
            imdbId: String? = null,
            mediaType: MediaType,
            season: Int = 1,
            episode: Int = 1
        ): Intent {
            return Intent(context, VideoPlayerActivity::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_TMDB_ID, tmdbId)
                putExtra(EXTRA_IMDB_ID, imdbId)
                putExtra(EXTRA_MEDIA_TYPE, mediaType.name)
                putExtra(EXTRA_SEASON, season)
                putExtra(EXTRA_EPISODE, episode)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Make activity fullscreen
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_FULLSCREEN
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
        
        val title = intent.getStringExtra(EXTRA_TITLE) ?: ""
        val tmdbId = intent.getIntExtra(EXTRA_TMDB_ID, 0)
        val imdbId = intent.getStringExtra(EXTRA_IMDB_ID)
        val mediaType = MediaType.valueOf(intent.getStringExtra(EXTRA_MEDIA_TYPE) ?: "MOVIE")
        val season = intent.getIntExtra(EXTRA_SEASON, 1)
        val episode = intent.getIntExtra(EXTRA_EPISODE, 1)
        
        setContent {
            CinemaniaTheme {
                VideoPlayerScreen(
                    title = title,
                    tmdbId = tmdbId,
                    imdbId = imdbId,
                    mediaType = mediaType,
                    season = season,
                    episode = episode,
                    onBackPressed = { finish() }
                )
            }
        }
    }
    
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_BACK,
            KeyEvent.KEYCODE_ESCAPE -> {
                finish()
                true
            }
            else -> super.onKeyDown(keyCode, event)
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        exoPlayer?.release()
    }
}

@Composable
fun VideoPlayerScreen(
    title: String,
    tmdbId: Int,
    imdbId: String?,
    mediaType: MediaType,
    season: Int,
    episode: Int,
    onBackPressed: () -> Unit
) {
    var isLoading by remember { mutableStateOf(true) }
    var showControls by remember { mutableStateOf(true) }
    var isPlaying by remember { mutableStateOf(false) }
    var currentSource by remember { mutableStateOf("vidsrc") }
    var playerUrl by remember { mutableStateOf("") }
    
    // Generate streaming URL based on source
    LaunchedEffect(tmdbId, mediaType, season, episode, currentSource) {
        playerUrl = generateStreamingUrl(tmdbId, imdbId, mediaType, season, episode, currentSource)
        isLoading = false
    }
    
    // Auto-hide controls
    LaunchedEffect(showControls) {
        if (showControls && isPlaying) {
            delay(3000)
            showControls = false
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Video Player
        if (playerUrl.isNotEmpty()) {
            WebVideoPlayer(
                url = playerUrl,
                modifier = Modifier.fillMaxSize(),
                onPlayingChanged = { isPlaying = it }
            )
        }
        
        // Loading Indicator
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = Color.White,
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Loading $title...",
                        color = Color.White,
                        fontSize = 16.sp
                    )
                }
            }
        }
        
        // Controls Overlay
        if (showControls && !isLoading) {
            VideoControls(
                title = title,
                isPlaying = isPlaying,
                currentSource = currentSource,
                onBackPressed = onBackPressed,
                onPlayPausePressed = { /* Handle play/pause */ },
                onSourceChanged = { newSource ->
                    currentSource = newSource
                    isLoading = true
                },
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@Composable
fun VideoControls(
    title: String,
    isPlaying: Boolean,
    currentSource: String,
    onBackPressed: () -> Unit,
    onPlayPausePressed: () -> Unit,
    onSourceChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        // Top Controls
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp)
                .background(
                    Color.Black.copy(alpha = 0.7f),
                    RoundedCornerShape(8.dp)
                )
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackPressed) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Text(
                text = title,
                color = Color.White,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.weight(1f)
            )
            
            IconButton(onClick = { /* Show settings */ }) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = "Settings",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        // Center Play/Pause Button
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            IconButton(
                onClick = onPlayPausePressed,
                modifier = Modifier
                    .size(80.dp)
                    .background(
                        Color.Black.copy(alpha = 0.7f),
                        RoundedCornerShape(40.dp)
                    )
            ) {
                Icon(
                    imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                    contentDescription = if (isPlaying) "Pause" else "Play",
                    tint = Color.White,
                    modifier = Modifier.size(40.dp)
                )
            }
        }
        
        // Bottom Controls - Source Selection
        Row(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(24.dp)
                .background(
                    Color.Black.copy(alpha = 0.7f),
                    RoundedCornerShape(8.dp)
                )
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            val sources = listOf("vidsrc", "embedsu", "multiembed", "smashystream")
            
            sources.forEach { source ->
                Button(
                    onClick = { onSourceChanged(source) },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (currentSource == source) Color.White else Color.Transparent,
                        contentColor = if (currentSource == source) Color.Black else Color.White
                    ),
                    shape = RoundedCornerShape(6.dp)
                ) {
                    Text(
                        text = source.uppercase(),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

@Composable
fun WebVideoPlayer(
    url: String,
    modifier: Modifier = Modifier,
    onPlayingChanged: (Boolean) -> Unit = {}
) {
    // For now, we'll use a placeholder since WebView integration requires more setup
    // In a real implementation, you would use WebView to load the streaming URL
    Box(
        modifier = modifier.background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "Video Player\n$url",
            color = Color.White,
            fontSize = 16.sp
        )
    }
}

private fun generateStreamingUrl(
    tmdbId: Int,
    imdbId: String?,
    mediaType: MediaType,
    season: Int,
    episode: Int,
    source: String
): String {
    return when (source) {
        "vidsrc" -> {
            if (mediaType == MediaType.MOVIE) {
                "https://vidsrc.to/embed/movie/$tmdbId"
            } else {
                "https://vidsrc.to/embed/tv/$tmdbId/$season/$episode"
            }
        }
        "embedsu" -> {
            if (mediaType == MediaType.MOVIE) {
                "https://embed.su/embed/movie/$tmdbId"
            } else {
                "https://embed.su/embed/tv/$tmdbId/$season/$episode"
            }
        }
        "multiembed" -> {
            if (mediaType == MediaType.MOVIE) {
                "https://multiembed.mov/directstream.php?video_id=$tmdbId&tmdb=1"
            } else {
                "https://multiembed.mov/directstream.php?video_id=$tmdbId&tmdb=1&s=$season&e=$episode"
            }
        }
        "smashystream" -> {
            if (mediaType == MediaType.MOVIE) {
                "https://player.smashy.stream/movie/$tmdbId"
            } else {
                "https://player.smashy.stream/tv/$tmdbId?s=$season&e=$episode"
            }
        }
        else -> ""
    }
}
