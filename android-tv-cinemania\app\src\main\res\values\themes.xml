<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme for Android TV -->
    <style name="Theme.Cinemania" parent="Theme.Leanback">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/cinemania_red</item>
        <item name="colorPrimaryVariant">@color/cinemania_red_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/dark_gray</item>
        <item name="colorSecondaryVariant">@color/medium_gray</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/black</item>
        <item name="colorSurface">@color/dark_surface</item>
        <item name="colorOnSurface">@color/white</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/black</item>
        
        <!-- Window background -->
        <item name="android:windowBackground">@color/black</item>
        
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/light_gray</item>
        
        <!-- Focus and selection -->
        <item name="android:colorFocusedHighlight">@color/cinemania_red</item>
        <item name="android:colorActivatedHighlight">@color/cinemania_red_transparent</item>
    </style>
    
    <!-- Fullscreen theme for video player -->
    <style name="Theme.Cinemania.Fullscreen" parent="Theme.Cinemania">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowBackground">@color/black</item>
    </style>
    
    <!-- Splash screen theme -->
    <style name="Theme.Cinemania.Splash" parent="Theme.Cinemania">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
    </style>
</resources>
